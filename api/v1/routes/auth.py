"""
Authentication endpoints.
"""
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from datetime import datetime, timedelta
from utils.security import create_access_token
from utils.config import settings

router = APIRouter()

@router.post("/token", tags=["auth"])
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    """
    OAuth2 compatible token login, get an access token for future requests.
    
    Args:
        form_data: OAuth2 password request form
        
    Returns:
        dict: Access token and token type
    """
    # This is a placeholder implementation
    return {
        "access_token": "placeholder_token",
        "token_type": "bearer"
    }

@router.post("/logout", tags=["auth"])
async def logout():
    """
    Logout endpoint.

    Returns:
        dict: Success message
    """
    return {"message": "Successfully logged out"}

@router.post("/test-token", tags=["auth"])
async def get_test_token():
    """
    Get a test token for development purposes.

    Returns:
        dict: Access token and token type
    """
    # Create a test token with test user data
    user_data = {
        "sub": "00000000-0000-0000-0000-000000000000",
        "username": "testuser",
        "email": "<EMAIL>",
        "is_active": True,
        "is_superuser": True
    }

    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(user_data, expires_delta=access_token_expires)

    return {
        "access_token": access_token,
        "token_type": "bearer"
    }
