"""
File upload service.
"""
from fastapi import HTTPException, status, UploadFile
from sqlalchemy.orm import Session
from uuid import UUID
from typing import List, Optional, Tuple
import logging
import os
import shutil
import hashlib
from datetime import datetime

from db.models.file_upload import FileUpload
from db.schemas.file_upload import FileUploadUpdate

logger = logging.getLogger(__name__)

class FileUploadService:
    """Service for file upload operations."""

    def __init__(self, db: Session):
        """Initialize the service with a database session."""
        self.db = db
        self.upload_dir = os.environ.get("UPLOAD_DIR", "/tmp/uploads")

        # Ensure upload directory exists
        os.makedirs(self.upload_dir, exist_ok=True)

    def get_all(self, skip: int = 0, limit: int = 100, user_id: UUID = None) -> Tuple[List[FileUpload], int]:
        """Get all file uploads with optional filtering."""
        query = self.db.query(FileUpload)
        if user_id:
            query = query.filter(FileUpload.owner_id == user_id)

        total = query.count()
        items = query.offset(skip).limit(limit).all()
        return items, total

    def get_by_id(self, file_upload_id: UUID, user_id: UUID = None) -> Optional[FileUpload]:
        """Get file upload by ID with optional user_id check."""
        query = self.db.query(FileUpload).filter(FileUpload.id == file_upload_id)
        if user_id:
            query = query.filter(FileUpload.owner_id == user_id)
        return query.first()

    async def create(self, file: UploadFile, user_id: UUID) -> FileUpload:
        """Create a new file upload."""
        try:
            # Generate a unique filename
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            unique_filename = f"{timestamp}_{file.filename}"
            file_path = os.path.join(self.upload_dir, unique_filename)

            # Save the file
            try:
                with open(file_path, "wb") as buffer:
                    shutil.copyfileobj(file.file, buffer)
            except Exception as e:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Error saving file: {str(e)}"
                )

            # Get file size and calculate hash
            try:
                file_size = os.path.getsize(file_path)

                # Calculate file hash
                hash_sha256 = hashlib.sha256()
                with open(file_path, "rb") as f:
                    for chunk in iter(lambda: f.read(4096), b""):
                        hash_sha256.update(chunk)
                file_hash = hash_sha256.hexdigest()

            except Exception as e:
                # Clean up the file if we can't get its size or hash
                if os.path.exists(file_path):
                    os.remove(file_path)
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Error processing file: {str(e)}"
                )

            # Create database record
            download_url = f"/api/v1/file_upload/{unique_filename}/download"
            db_file_upload = FileUpload(
                filename=file.filename,
                file_path=file_path,
                content_type=file.content_type,
                file_size=file_size,
                file_hash=file_hash,
                download_url=download_url,
                owner_id=user_id
            )

            self.db.add(db_file_upload)
            self.db.commit()
            self.db.refresh(db_file_upload)
            return db_file_upload
        except HTTPException:
            # Re-raise HTTP exceptions
            raise
        except Exception as e:
            # Clean up the file if it exists
            if 'file_path' in locals() and os.path.exists(file_path):
                os.remove(file_path)

            # Roll back the transaction if needed
            self.db.rollback()

            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error creating file upload: {str(e)}"
            )

    def update(self, file_upload_id: UUID, file_upload_data: FileUploadUpdate, user_id: UUID = None) -> FileUpload:
        """Update a file upload."""
        db_file_upload = self.get_by_id(file_upload_id, user_id)
        if not db_file_upload:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"File upload with id {file_upload_id} not found"
            )

        # Update only provided fields
        for key, value in file_upload_data.dict(exclude_unset=True).items():
            setattr(db_file_upload, key, value)

        self.db.commit()
        self.db.refresh(db_file_upload)
        return db_file_upload

    def delete(self, file_upload_id: UUID, user_id: UUID = None) -> None:
        """Delete a file upload."""
        db_file_upload = self.get_by_id(file_upload_id, user_id)
        if not db_file_upload:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"File upload with id {file_upload_id} not found"
            )

        try:
            # Delete the file if it exists
            if os.path.exists(db_file_upload.file_path):
                try:
                    os.remove(db_file_upload.file_path)
                except Exception as e:
                    # Log the error but continue with database deletion
                    logger.warning(f"Error deleting file {db_file_upload.file_path}: {str(e)}")

            # Delete the database record
            self.db.delete(db_file_upload)
            self.db.commit()
        except Exception as e:
            # Roll back the transaction
            self.db.rollback()

            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error deleting file upload: {str(e)}"
            )
