# Task T1: Create initial PostgreSQL database setup script

Status: done
Priority: high

## Description

Create a script to initialize the PostgreSQL database with basic configuration.

## Implementation Details

Create a shell script that sets up the PostgreSQL database with the specified configuration (host: localhost, port: 5430, database name: turdparty, user: postgres, password: postgres). The script should check if the database already exists before attempting to create it, and should handle error cases appropriately.

## Test Strategy

Verify the script successfully creates the database with correct configuration by connecting to it and running a simple query. Test error handling by running the script on an already configured system.

