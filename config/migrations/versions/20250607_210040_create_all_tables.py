"""create_all_tables

Revision ID: 997eeffc15b3
Revises: 
Create Date: 2025-06-07 21:00:40.737534+00:00

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# Data migration imports
from uuid import uuid4
from sqlalchemy.orm import Session
from datetime import datetime

# Revision identifiers, used by Alembic.
revision = '997eeffc15b3'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('users',
    sa.Column('username', sa.String(length=50), nullable=False),
    sa.Column('email', sa.String(length=255), nullable=False),
    sa.Column('full_name', sa.String(length=100), nullable=False),
    sa.Column('password_hash', sa.String(length=255), nullable=False),
    sa.Column('is_active', sa.<PERSON>(), nullable=False),
    sa.Column('is_superuser', sa.<PERSON>(), nullable=False),
    sa.Column('roles', sa.String(length=255), nullable=False),
    sa.Column('mfa_enabled', sa.Boolean(), nullable=False),
    sa.Column('mfa_secret', sa.String(length=32), nullable=True),
    sa.Column('last_login', sa.DateTime(), nullable=True),
    sa.Column('failed_login_attempts', sa.Integer(), nullable=False),
    sa.Column('locked_until', sa.DateTime(), nullable=True),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_on', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('modified_on', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('deleted_on', sa.DateTime(), nullable=True),
    sa.CheckConstraint('LENGTH(full_name) BETWEEN 1 AND 100', name='ck_full_name_length'),
    sa.CheckConstraint('LENGTH(mfa_secret) = 32', name='ck_mfa_secret_length'),
    sa.CheckConstraint('LENGTH(password_hash) > 10', name='ck_password_hash_length'),
    sa.CheckConstraint('LENGTH(roles) BETWEEN 1 AND 255', name='ck_roles_length'),
    sa.CheckConstraint('LENGTH(username) BETWEEN 3 AND 50', name='ck_username_length'),
    sa.PrimaryKeyConstraint('id', name='pk_users'),
    sa.UniqueConstraint('email', name='uq_users_email'),
    sa.UniqueConstraint('username', name='uq_users_username')
    )
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.create_index('ix_users_username_email', ['username', 'email'], unique=False)

    op.create_table('audit_logs',
    sa.Column('action', sa.String(length=100), nullable=False),
    sa.Column('resource_type', sa.String(length=50), nullable=True),
    sa.Column('resource_id', sa.String(length=255), nullable=True),
    sa.Column('user_id', sa.UUID(), nullable=True),
    sa.Column('ip_address', sa.String(length=45), nullable=True),
    sa.Column('user_agent', sa.String(length=500), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('event_metadata', sa.JSON(), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('timestamp', sa.DateTime(), nullable=False),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_on', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('modified_on', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('deleted_on', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('audit_logs', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_audit_logs_action'), ['action'], unique=False)
        batch_op.create_index(batch_op.f('ix_audit_logs_resource_id'), ['resource_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_audit_logs_resource_type'), ['resource_type'], unique=False)
        batch_op.create_index(batch_op.f('ix_audit_logs_timestamp'), ['timestamp'], unique=False)

    op.create_table('hash_reports',
    sa.Column('file_hash', sa.String(length=64), nullable=False),
    sa.Column('report_data', sa.Text(), nullable=False),
    sa.Column('owner_id', sa.UUID(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_on', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('modified_on', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('deleted_on', sa.DateTime(), nullable=True),
    sa.CheckConstraint('LENGTH(file_hash) = 64', name='ck_file_hash_length'),
    sa.ForeignKeyConstraint(['owner_id'], ['users.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('hash_reports', schema=None) as batch_op:
        batch_op.create_index('ix_hash_reports_file_hash', ['file_hash'], unique=False)
        batch_op.create_index('ix_hash_reports_owner_id', ['owner_id'], unique=False)

    op.create_table('items',
    sa.Column('title', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('owner_id', sa.UUID(), nullable=False),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_on', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('modified_on', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('deleted_on', sa.DateTime(), nullable=True),
    sa.CheckConstraint('LENGTH(title) BETWEEN 3 AND 255', name='ck_title_length'),
    sa.ForeignKeyConstraint(['owner_id'], ['users.id'], onupdate='CASCADE', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name='pk_items')
    )
    with op.batch_alter_table('items', schema=None) as batch_op:
        batch_op.create_index('ix_items_owner_id', ['owner_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_items_title'), ['title'], unique=False)

    op.create_table('sessions',
    sa.Column('token', sa.String(length=255), nullable=False),
    sa.Column('expires_at', sa.DateTime(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('ip_address', sa.String(length=45), nullable=True),
    sa.Column('user_agent', sa.String(length=500), nullable=True),
    sa.Column('last_activity', sa.DateTime(), nullable=True),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_on', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('modified_on', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('deleted_on', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('sessions', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_sessions_token'), ['token'], unique=True)

    op.create_table('vagrant_vms',
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('template', sa.String(length=100), nullable=False),
    sa.Column('memory_mb', sa.Integer(), nullable=False),
    sa.Column('cpus', sa.Integer(), nullable=False),
    sa.Column('disk_gb', sa.Integer(), nullable=False),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('ip_address', sa.String(length=50), nullable=True),
    sa.Column('ssh_port', sa.Integer(), nullable=True),
    sa.Column('vagrant_id', sa.String(length=100), nullable=True),
    sa.Column('last_action', sa.String(length=50), nullable=True),
    sa.Column('last_action_time', sa.DateTime(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('domain', sa.String(length=100), nullable=False),
    sa.Column('cpu_usage', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('memory_usage', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('disk_usage', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('uptime_seconds', sa.Integer(), nullable=False),
    sa.Column('owner_id', sa.UUID(), nullable=False),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_on', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('modified_on', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('deleted_on', sa.DateTime(), nullable=True),
    sa.CheckConstraint('LENGTH(domain) BETWEEN 1 AND 100', name='ck_domain_length'),
    sa.CheckConstraint('LENGTH(name) BETWEEN 3 AND 100', name='ck_vm_name_length'),
    sa.CheckConstraint('LENGTH(status) BETWEEN 1 AND 20', name='ck_status_length'),
    sa.CheckConstraint('LENGTH(template) BETWEEN 1 AND 100', name='ck_template_length'),
    sa.CheckConstraint('cpus BETWEEN 1 AND 16', name='ck_cpus_range'),
    sa.CheckConstraint('disk_gb BETWEEN 1 AND 500', name='ck_disk_range'),
    sa.CheckConstraint('memory_mb BETWEEN 512 AND 32768', name='ck_memory_range'),
    sa.ForeignKeyConstraint(['owner_id'], ['users.id'], onupdate='CASCADE', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name='pk_vagrant_vms')
    )
    with op.batch_alter_table('vagrant_vms', schema=None) as batch_op:
        batch_op.create_index('ix_vagrant_vms_name', ['name'], unique=False)
        batch_op.create_index('ix_vagrant_vms_owner_id', ['owner_id'], unique=False)
        batch_op.create_index('ix_vagrant_vms_status', ['status'], unique=False)

    op.create_table('file_uploads',
    sa.Column('filename', sa.String(length=255), nullable=False),
    sa.Column('file_size', sa.Integer(), nullable=False),
    sa.Column('content_type', sa.String(length=100), nullable=False),
    sa.Column('file_hash', sa.String(length=64), nullable=False),
    sa.Column('file_path', sa.String(length=255), nullable=False),
    sa.Column('file_folder_path', sa.String(length=255), nullable=True),
    sa.Column('folder_id', sa.String(length=100), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('download_url', sa.String(length=255), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('owner_id', sa.UUID(), nullable=False),
    sa.Column('vm_id', sa.UUID(), nullable=True),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_on', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('modified_on', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('deleted_on', sa.DateTime(), nullable=True),
    sa.CheckConstraint('LENGTH(file_hash) = 64', name='ck_file_hash_length'),
    sa.CheckConstraint('LENGTH(filename) BETWEEN 1 AND 255', name='ck_filename_length'),
    sa.ForeignKeyConstraint(['owner_id'], ['users.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['vm_id'], ['vagrant_vms.id'], ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('file_uploads', schema=None) as batch_op:
        batch_op.create_index('ix_file_uploads_folder_id', ['folder_id'], unique=False)
        batch_op.create_index('ix_file_uploads_owner_id', ['owner_id'], unique=False)
        batch_op.create_index('ix_file_uploads_vm_id', ['vm_id'], unique=False)

    op.create_table('file_selections',
    sa.Column('file_upload_id', sa.UUID(), nullable=False),
    sa.Column('owner_id', sa.UUID(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_on', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('modified_on', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('deleted_on', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['file_upload_id'], ['file_uploads.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['owner_id'], ['users.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('file_selections', schema=None) as batch_op:
        batch_op.create_index('ix_file_selections_file_upload_id', ['file_upload_id'], unique=False)
        batch_op.create_index('ix_file_selections_owner_id', ['owner_id'], unique=False)

    op.create_table('vm_injections',
    sa.Column('vagrant_vm_id', sa.UUID(), nullable=False),
    sa.Column('file_selection_id', sa.UUID(), nullable=False),
    sa.Column('owner_id', sa.UUID(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('additional_command', sa.Text(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('completed_on', sa.DateTime(), nullable=True),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_on', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('modified_on', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('deleted_on', sa.DateTime(), nullable=True),
    sa.CheckConstraint('LENGTH(status) BETWEEN 1 AND 20', name='ck_status_length'),
    sa.ForeignKeyConstraint(['file_selection_id'], ['file_selections.id'], onupdate='CASCADE', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['owner_id'], ['users.id'], onupdate='CASCADE', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['vagrant_vm_id'], ['vagrant_vms.id'], onupdate='CASCADE', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name='pk_vm_injections')
    )
    with op.batch_alter_table('vm_injections', schema=None) as batch_op:
        batch_op.create_index('ix_vm_injections_owner_id', ['owner_id'], unique=False)
        batch_op.create_index('ix_vm_injections_vagrant_vm_id', ['vagrant_vm_id'], unique=False)

    # ### end Alembic commands ###
    
    # Data migration
    # Uncomment this block if you need to perform data migration
    # connection = op.get_bind()
    # session = Session(bind=connection)
    # try:
    #     # Perform data migration here
    #     # Example:
    #     # for row in session.execute("SELECT id FROM table"):
    #     #     session.execute("UPDATE table SET new_column = 'value' WHERE id = :id", {'id': row[0]})
    #     session.commit()
    # except Exception as e:
    #     session.rollback()
    #     raise e
    # finally:
    #     session.close()


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('vm_injections', schema=None) as batch_op:
        batch_op.drop_index('ix_vm_injections_vagrant_vm_id')
        batch_op.drop_index('ix_vm_injections_owner_id')

    op.drop_table('vm_injections')
    with op.batch_alter_table('file_selections', schema=None) as batch_op:
        batch_op.drop_index('ix_file_selections_owner_id')
        batch_op.drop_index('ix_file_selections_file_upload_id')

    op.drop_table('file_selections')
    with op.batch_alter_table('file_uploads', schema=None) as batch_op:
        batch_op.drop_index('ix_file_uploads_vm_id')
        batch_op.drop_index('ix_file_uploads_owner_id')
        batch_op.drop_index('ix_file_uploads_folder_id')

    op.drop_table('file_uploads')
    with op.batch_alter_table('vagrant_vms', schema=None) as batch_op:
        batch_op.drop_index('ix_vagrant_vms_status')
        batch_op.drop_index('ix_vagrant_vms_owner_id')
        batch_op.drop_index('ix_vagrant_vms_name')

    op.drop_table('vagrant_vms')
    with op.batch_alter_table('sessions', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_sessions_token'))

    op.drop_table('sessions')
    with op.batch_alter_table('items', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_items_title'))
        batch_op.drop_index('ix_items_owner_id')

    op.drop_table('items')
    with op.batch_alter_table('hash_reports', schema=None) as batch_op:
        batch_op.drop_index('ix_hash_reports_owner_id')
        batch_op.drop_index('ix_hash_reports_file_hash')

    op.drop_table('hash_reports')
    with op.batch_alter_table('audit_logs', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_audit_logs_timestamp'))
        batch_op.drop_index(batch_op.f('ix_audit_logs_resource_type'))
        batch_op.drop_index(batch_op.f('ix_audit_logs_resource_id'))
        batch_op.drop_index(batch_op.f('ix_audit_logs_action'))

    op.drop_table('audit_logs')
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.drop_index('ix_users_username_email')

    op.drop_table('users')
    # ### end Alembic commands ###
